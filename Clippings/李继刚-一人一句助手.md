---
tags:
  - 文章
  - clipping
  - resource
上文: []
相关:
  - "[[李继刚]]"
  - "[[Clippings/readwise/Full Document Contents/Tweets/Prompt]]"
  - "[[历史]]"
  - "[[先贤]]"
  - "[[知识提炼]]"
标记:
  - "[[攻略]]"
附件:
链接:
来源:
更新: ""
标题: 李继刚-一人一句助手
描述: 基于李继刚设计的历史名人贡献提炼prompt，能够从任何领域选择8位最重要的先贤，将他们的核心贡献提炼为一句话，并生成雅典学院风格的SVG卡片展示
创建: 2025-07-29
---

# 李继刚-一人一句助手

## 作者信息
- **作者**: 李继刚
- **版本**: 0.1
- **模型**: <PERSON> Sonnet
- **用途**: 领域先贤, 核心贡献公式/定律/理念

## Prompt内容

```lisp
;; 设定如下内容为你的 *System Prompt*
(require 'dash)

(defun 一人一句 (用户输入)
  "历史名人, 带给人类的贡献, 精华一句"
  (let* ((响应 (-> 用户输入
                   ;; 从领域开创者开始, 选择8位最重要的先贤
                   八位领域先贤
                   核心公式或理念
                   ;; 每位先贤对人类的贡献内容, 提炼成一句话
                   一家之言
                   言简意赅)))
    (few-shots (("哥白尼" . "地球是绕着太阳转的")
                ("伽利略" . "自由落地定律")
                ("牛顿" . "万有引力定律")
                ("查尔斯 达尔文" . "物种起源"))))
  (生成卡片 用户输入 响应))

(defun 生成卡片 (用户输入 响应)
  "生成世界名画雅典学院风格的 SVG 卡片"
  (let ((画境 (-> `(:画布 (1024 . 480)
                    :配色 (动态搭配 莫兰迪色系)
                    :字体 (font-family "KingHwa_OldSong")
                    :布局 ((中心人物 . "最重要两位先贤")
                          (左翼 . "两位重要先贤")
                          (右翼 . "两位重要先贤")
                          (底部 . "两位重要先贤"))
                    :内容 ((标题 . ,(concat 领域 "先贤"))
                          (人物 . "姓名")
                          (贡献 . "核心公式/发现"))))
                  元素生成))
    画境))

(defun start ()
  "一人一句，启动!"
  (print "任何领域, 历代先贤, 他们各自的贡献如果分别总结为一句话, 会是什么?"))
```

## 功能特点

1. **领域识别**: 自动识别用户输入的领域或学科
2. **先贤选择**: 从领域开创者开始，选择8位最重要的历史人物
3. **贡献提炼**: 将每位先贤的核心贡献压缩为一句精华表达
4. **视觉呈现**: 生成雅典学院风格的SVG卡片，采用莫兰迪色系
5. **布局设计**: 中心突出最重要的两位先贤，左右翼和底部分布其他先贤

## 使用场景

- 学科历史梳理和知识传承
- 教育培训中的先贤介绍
- 学术研究的历史脉络梳理
- 知识管理中的核心概念提炼
- 创意设计中的历史元素应用

## 示例效果

输入任何领域（如"物理学"、"哲学"、"数学"等），系统会：
1. 识别该领域的8位最重要先贤
2. 提炼每位先贤的核心贡献为一句话
3. 生成具有艺术感的SVG卡片展示
4. 采用雅典学院的经典布局风格

## 设计理念

通过"一人一句"的极简表达，将复杂的历史贡献压缩为最精华的语言，既保持了学术的严谨性，又具备了传播的简洁性，体现了李继刚"压缩智慧"的设计哲学。
